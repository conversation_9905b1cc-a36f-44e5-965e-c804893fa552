import json
import re
from typing import Any, Dict, List, Optional
from datetime import datetime

from langchain_core.messages import HumanMessage, SystemMessage

from ..state.agent_state import AgentState
from ..utils.llm_utils import LLMConfig


class EmailAgent:
    """Agent that handles email composition and generation via natural language interface."""

    def __init__(self, llm_config: LLMConfig):
        self.llm = llm_config.get_llm()
        self.system_prompt = """
        You are an Email Agent that helps users compose structured emails via natural language commands.
        
        Your primary responsibilities:
        1. Parse user input that starts with /email command
        2. Extract email components (recipient, subject, body, etc.)
        3. Generate structured JSON output for email composition
        4. Always return valid JSON regardless of input quality
        
        JSON Output Format:
        {
            "email_data": {
                "to": ["<EMAIL>", "<EMAIL>"],
                "cc": ["<EMAIL>"],
                "bcc": ["<EMAIL>"],
                "subject": "Email Subject",
                "body": "Email body content",
                "body_type": "plain" or "html",
                "priority": "normal", "high", or "low",
                "attachments": ["file1.pdf", "file2.doc"],
                "template": "template_name" (optional),
                "scheduled_send": "2024-01-01T10:00:00Z" (optional)
            },
            "validation": {
                "is_valid": true/false,
                "missing_fields": ["field1", "field2"],
                "warnings": ["warning1", "warning2"]
            },
            "metadata": {
                "command_detected": true/false,
                "processing_timestamp": "2024-01-01T10:00:00Z",
                "agent_version": "1.0"
            }
        }
        
        Processing Rules:
        1. If input doesn't start with /email, set command_detected to false but still return JSON
        2. Extract email addresses using regex patterns
        3. If recipient is missing, mark as invalid and add to missing_fields
        4. If subject is missing, generate a default subject or mark as missing
        5. If body is missing, mark as invalid
        6. Always include validation status and metadata
        7. Handle partial or incomplete requests gracefully
        8. Support natural language patterns like "send <NAME_EMAIL> about meeting"
        
        Example Input Patterns:
        - "/<NAME_EMAIL> subject 'Meeting Tomorrow' body 'Hi John, let's meet tomorrow at 2 PM'"
        - "/email <NAME_EMAIL> cc <EMAIL> about project update"
        - "/email compose <NAME_EMAIL> regarding invoice #12345"
        """

    def _extract_email_addresses(self, text: str) -> List[str]:
        """Extract email addresses from text using regex."""
        email_pattern = r'\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b'
        return re.findall(email_pattern, text, re.IGNORECASE)

    def _parse_email_command(self, query: str) -> Dict[str, Any]:
        """Parse email command and extract components."""
        # Initialize email data structure
        email_data = {
            "to": [],
            "cc": [],
            "bcc": [],
            "subject": "",
            "body": "",
            "body_type": "plain",
            "priority": "normal",
            "attachments": [],
            "template": None,
            "scheduled_send": None
        }
        
        validation = {
            "is_valid": True,
            "missing_fields": [],
            "warnings": []
        }
        
        # Check if command starts with /email
        command_detected = query.strip().lower().startswith('/email')
        
        if not command_detected:
            validation["is_valid"] = False
            validation["warnings"].append("Query does not start with /email command")
            return email_data, validation, command_detected
        
        # Remove /email prefix for processing
        content = query[6:].strip()
        
        # Extract email addresses
        all_emails = self._extract_email_addresses(content)
        
        # Parse different sections
        to_match = re.search(r'(?:to|send to)\s+([^,\s]+(?:@[^,\s]+)?(?:,\s*[^,\s]+(?:@[^,\s]+)?)*)', content, re.IGNORECASE)
        cc_match = re.search(r'cc\s+([^,\s]+(?:@[^,\s]+)?(?:,\s*[^,\s]+(?:@[^,\s]+)?)*)', content, re.IGNORECASE)
        bcc_match = re.search(r'bcc\s+([^,\s]+(?:@[^,\s]+)?(?:,\s*[^,\s]+(?:@[^,\s]+)?)*)', content, re.IGNORECASE)
        
        subject_match = re.search(r'(?:subject|about|regarding)\s+["\']([^"\']+)["\']', content, re.IGNORECASE)
        if not subject_match:
            subject_match = re.search(r'(?:subject|about|regarding)\s+([^\n\r]+?)(?:\s+body|\s+message|$)', content, re.IGNORECASE)
        
        body_match = re.search(r'(?:body|message|content)\s+["\']([^"\']+)["\']', content, re.IGNORECASE)
        if not body_match:
            body_match = re.search(r'(?:body|message|content)\s+([^\n\r]+)', content, re.IGNORECASE)
        
        # Populate email data
        if to_match:
            to_emails = self._extract_email_addresses(to_match.group(1))
            email_data["to"] = to_emails if to_emails else [to_match.group(1).strip()]
        elif all_emails:
            # If no explicit "to" but emails found, use first as recipient
            email_data["to"] = [all_emails[0]]
            
        if cc_match:
            cc_emails = self._extract_email_addresses(cc_match.group(1))
            email_data["cc"] = cc_emails if cc_emails else [cc_match.group(1).strip()]
            
        if bcc_match:
            bcc_emails = self._extract_email_addresses(bcc_match.group(1))
            email_data["bcc"] = bcc_emails if bcc_emails else [bcc_match.group(1).strip()]
        
        if subject_match:
            email_data["subject"] = subject_match.group(1).strip()
        
        if body_match:
            email_data["body"] = body_match.group(1).strip()
        
        # Validation
        if not email_data["to"]:
            validation["is_valid"] = False
            validation["missing_fields"].append("recipient")
            
        if not email_data["subject"]:
            validation["missing_fields"].append("subject")
            
        if not email_data["body"]:
            validation["is_valid"] = False
            validation["missing_fields"].append("body")
        
        return email_data, validation, command_detected

    async def process_query(self, query: str, state: AgentState) -> Dict[str, Any]:
        """Process an email query and generate structured JSON output."""
        print(f"[INFO] EmailAgent: Processing query")
        
        try:
            # Parse the email command
            email_data, validation, command_detected = self._parse_email_command(query)
            
            # If parsing failed or incomplete, use LLM to enhance
            if not validation["is_valid"] or validation["missing_fields"]:
                enhanced_data = await self._enhance_with_llm(query, email_data, state)
                if enhanced_data:
                    email_data.update(enhanced_data)
                    # Re-validate after enhancement
                    if email_data.get("to") and email_data.get("body"):
                        validation["is_valid"] = True
                        validation["missing_fields"] = [field for field in validation["missing_fields"] 
                                                      if field not in ["recipient", "body"]]
            
            # Generate final JSON structure
            result = {
                "email_data": email_data,
                "validation": validation,
                "metadata": {
                    "command_detected": command_detected,
                    "processing_timestamp": datetime.now().isoformat(),
                    "agent_version": "1.0"
                }
            }
            
            return {"response": json.dumps(result, indent=2)}
            
        except Exception as e:
            print(f"[ERROR] EmailAgent: {str(e)}")
            # Return error JSON structure
            error_result = {
                "email_data": {
                    "to": [], "cc": [], "bcc": [], "subject": "", "body": "",
                    "body_type": "plain", "priority": "normal", "attachments": [],
                    "template": None, "scheduled_send": None
                },
                "validation": {
                    "is_valid": False,
                    "missing_fields": ["all"],
                    "warnings": [f"Processing error: {str(e)}"]
                },
                "metadata": {
                    "command_detected": False,
                    "processing_timestamp": datetime.now().isoformat(),
                    "agent_version": "1.0"
                }
            }
            return {"response": json.dumps(error_result, indent=2)}

    async def _enhance_with_llm(self, query: str, email_data: Dict[str, Any], state: AgentState) -> Optional[Dict[str, Any]]:
        """Use LLM to enhance incomplete email data."""
        try:
            language = state.get("language", "en-US")

            enhancement_prompt = f"""
            Analyze this email request and extract missing information:

            Original Query: {query}
            Current Data: {json.dumps(email_data, indent=2)}

            Please provide ONLY the missing or improved fields in JSON format.
            If you can infer a reasonable subject from the context, include it.
            If you can extract or improve the email body, include it.
            Do not include fields that are already properly filled.

            Return only valid JSON with the fields that need to be added or updated.
            """

            messages = [
                SystemMessage(content=enhancement_prompt),
                HumanMessage(content=f"Enhance this email data: {query}")
            ]

            response = self.llm.invoke(messages)
            enhanced_json = response.content.strip()

            # Try to parse the LLM response as JSON
            if enhanced_json.startswith('{') and enhanced_json.endswith('}'):
                return json.loads(enhanced_json)

        except Exception as e:
            print(f"[WARNING] EmailAgent LLM enhancement failed: {str(e)}")

        return None

    async def run(self, state: AgentState) -> AgentState:
        """Run the email agent and update the state."""
        print("-----" * 20)
        print(f"[START] Email Agent")

        query = state["agent_queries"].get("email_agent", state["query"])
        results = {}

        if isinstance(query, list):
            for q in query:
                result = await self.process_query(q, state)
                results[q] = result["response"]
        else:
            result = await self.process_query(query, state)
            results[query] = result["response"]

        # Store results in agent outputs
        state["agent_outputs"]["email_agent"] = json.dumps(results)

        # Store email output in dedicated field
        if "email_output" not in state:
            state["email_output"] = {}

        # Parse and store the structured email data
        try:
            if isinstance(query, str):
                email_result = json.loads(result["response"])
                state["email_output"] = email_result
            else:
                # Handle multiple queries
                state["email_output"] = {"multiple_emails": results}
        except json.JSONDecodeError:
            state["email_output"] = {"error": "Failed to parse email JSON"}

        # Mark email agent as executed
        state["email_agent_executed"] = True

        print(f"[END] Email Agent")
        print("-----" * 20)

        return state
