import json
from typing import Dict, Any
from datetime import datetime
from fastapi import APIRouter, HTTPException, BackgroundTasks, Depends
from pydantic import BaseModel, EmailStr, validator
from typing import List, Optional

from api.utils.email_config import email_service, EmailData
from api.routes.authentication import get_current_admin
from api.database.user import User

email_router = APIRouter()


class SendEmailRequest(BaseModel):
    """Request model for sending emails."""
    email_data: Dict[str, Any]
    
    @validator('email_data')
    def validate_email_data(cls, v):
        """Validate email data structure."""
        required_fields = ['to', 'subject', 'body']
        for field in required_fields:
            if field not in v:
                raise ValueError(f"Missing required field: {field}")
        
        # Validate email addresses
        if not isinstance(v['to'], list) or not v['to']:
            raise ValueError("'to' field must be a non-empty list")
        
        return v


class EmailStatusResponse(BaseModel):
    """Response model for email operations."""
    success: bool
    message: str
    timestamp: str
    data: Optional[Dict[str, Any]] = None
    errors: Optional[List[str]] = None


class EmailConfigResponse(BaseModel):
    """Response model for email configuration."""
    is_valid: bool
    config: Dict[str, Any]
    issues: List[str]


@email_router.post("/send-email", response_model=EmailStatusResponse)
async def send_email(
    request: SendEmailRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_admin)
):
    """
    Send an email using the structured JSON data from the Email Agent.
    
    This endpoint accepts the JSON output from the Email Agent and dispatches
    the email via SMTP using the pre-configured sender email account.
    """
    try:
        # Extract email data from request
        email_data_dict = request.email_data
        
        # Validate and create EmailData object
        try:
            email_data = EmailData(**email_data_dict)
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid email data format: {str(e)}"
            )
        
        # Send email in background task for better performance
        background_tasks.add_task(send_email_task, email_data, current_user.username)
        
        return EmailStatusResponse(
            success=True,
            message="Email queued for sending",
            timestamp=datetime.now().isoformat(),
            data={
                "recipients": email_data.to,
                "subject": email_data.subject,
                "queued_by": current_user.username
            }
        )
        
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to process email request: {str(e)}"
        )


@email_router.post("/send-email-sync", response_model=EmailStatusResponse)
async def send_email_sync(
    request: SendEmailRequest,
    current_user: User = Depends(get_current_admin)
):
    """
    Send an email synchronously (wait for completion).
    
    Use this endpoint when you need immediate feedback on email delivery status.
    """
    try:
        # Extract and validate email data
        email_data_dict = request.email_data
        
        try:
            email_data = EmailData(**email_data_dict)
        except Exception as e:
            raise HTTPException(
                status_code=400,
                detail=f"Invalid email data format: {str(e)}"
            )
        
        # Send email synchronously
        result = email_service.send_email(email_data)
        
        if result["success"]:
            return EmailStatusResponse(
                success=True,
                message=result["message"],
                timestamp=result["timestamp"],
                data=result
            )
        else:
            return EmailStatusResponse(
                success=False,
                message=result["message"],
                timestamp=result["timestamp"],
                errors=[result["message"]]
            )
            
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to send email: {str(e)}"
        )


@email_router.get("/config", response_model=EmailConfigResponse)
async def get_email_config(current_user: User = Depends(get_current_admin)):
    """
    Get email configuration status and validation.
    
    Returns the current email configuration (without sensitive data)
    and validation status.
    """
    try:
        config_status = email_service.validate_config()
        
        return EmailConfigResponse(
            is_valid=config_status["is_valid"],
            config=config_status["config"],
            issues=config_status["issues"]
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get email configuration: {str(e)}"
        )


@email_router.post("/test-connection", response_model=EmailStatusResponse)
async def test_email_connection(current_user: User = Depends(get_current_admin)):
    """
    Test the SMTP connection with current configuration.
    
    This endpoint tests the connection to the SMTP server without sending an email.
    """
    try:
        result = email_service.test_connection()
        
        return EmailStatusResponse(
            success=result["success"],
            message=result["message"],
            timestamp=result["timestamp"],
            data=result if result["success"] else None,
            errors=[result["message"]] if not result["success"] else None
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to test email connection: {str(e)}"
        )


def send_email_task(email_data: EmailData, user: str):
    """
    Background task for sending emails.
    
    This function is executed as a background task to avoid blocking
    the API response while the email is being sent.
    """
    try:
        result = email_service.send_email(email_data)
        
        if result["success"]:
            print(f"[EMAIL] Successfully sent email to {', '.join(email_data.to)} by user {user}")
        else:
            print(f"[EMAIL] Failed to send email to {', '.join(email_data.to)} by user {user}: {result['message']}")
            
    except Exception as e:
        print(f"[EMAIL] Background email task failed: {str(e)}")


# Additional utility endpoints

@email_router.post("/validate-email-json")
async def validate_email_json(
    email_json: Dict[str, Any],
    current_user: User = Depends(get_current_admin)
):
    """
    Validate email JSON structure without sending.
    
    This endpoint can be used to validate the JSON output from the Email Agent
    before attempting to send the email.
    """
    try:
        # Check if this is the full Email Agent output format
        if "email_data" in email_json:
            email_data_dict = email_json["email_data"]
            validation_info = email_json.get("validation", {})
            metadata = email_json.get("metadata", {})
        else:
            # Assume it's direct email data
            email_data_dict = email_json
            validation_info = {}
            metadata = {}
        
        # Validate email data structure
        try:
            email_data = EmailData(**email_data_dict)
            validation_success = True
            validation_errors = []
        except Exception as e:
            validation_success = False
            validation_errors = [str(e)]
        
        return {
            "valid": validation_success,
            "errors": validation_errors,
            "email_data": email_data_dict,
            "agent_validation": validation_info,
            "metadata": metadata,
            "timestamp": datetime.now().isoformat()
        }
        
    except Exception as e:
        raise HTTPException(
            status_code=400,
            detail=f"Failed to validate email JSON: {str(e)}"
        )
