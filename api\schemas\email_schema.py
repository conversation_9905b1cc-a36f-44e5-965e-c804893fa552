from datetime import datetime
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Any, Dict, List, Optional


class EmailDataSchema(BaseModel):
    """Schema for email data structure."""
    to: List[EmailStr]
    cc: Optional[List[EmailStr]] = []
    bcc: Optional[List[EmailStr]] = []
    subject: str
    body: str
    body_type: str = Field(default="plain", regex="^(plain|html)$")
    priority: str = Field(default="normal", regex="^(low|normal|high)$")
    attachments: Optional[List[str]] = []
    template: Optional[str] = None
    scheduled_send: Optional[str] = None
    
    @validator('to')
    def validate_recipients(cls, v):
        if not v:
            raise ValueError('At least one recipient is required')
        return v
    
    @validator('subject')
    def validate_subject(cls, v):
        if not v.strip():
            raise ValueError('Subject cannot be empty')
        return v.strip()
    
    @validator('body')
    def validate_body(cls, v):
        if not v.strip():
            raise ValueError('Email body cannot be empty')
        return v.strip()


class EmailValidationSchema(BaseModel):
    """Schema for email validation information."""
    is_valid: bool
    missing_fields: List[str] = []
    warnings: List[str] = []


class EmailMetadataSchema(BaseModel):
    """Schema for email metadata."""
    command_detected: bool
    processing_timestamp: str
    agent_version: str


class EmailAgentOutputSchema(BaseModel):
    """Schema for complete Email Agent output."""
    email_data: EmailDataSchema
    validation: EmailValidationSchema
    metadata: EmailMetadataSchema


class SendEmailRequestSchema(BaseModel):
    """Schema for send email API request."""
    email_data: Dict[str, Any]
    
    @validator('email_data')
    def validate_email_data_dict(cls, v):
        required_fields = ['to', 'subject', 'body']
        for field in required_fields:
            if field not in v:
                raise ValueError(f"Missing required field: {field}")
        return v


class EmailStatusResponseSchema(BaseModel):
    """Schema for email operation responses."""
    success: bool
    message: str
    timestamp: str
    data: Optional[Dict[str, Any]] = None
    errors: Optional[List[str]] = None


class EmailConfigResponseSchema(BaseModel):
    """Schema for email configuration response."""
    is_valid: bool
    config: Dict[str, Any]
    issues: List[str]


class EmailValidationResponseSchema(BaseModel):
    """Schema for email validation response."""
    valid: bool
    errors: List[str]
    email_data: Dict[str, Any]
    agent_validation: Dict[str, Any]
    metadata: Dict[str, Any]
    timestamp: str


class EmailTemplateSchema(BaseModel):
    """Schema for email templates (future enhancement)."""
    template_id: str
    name: str
    subject_template: str
    body_template: str
    template_type: str = Field(default="plain", regex="^(plain|html)$")
    variables: List[str] = []
    created_at: datetime = Field(default_factory=datetime.now)
    updated_at: datetime = Field(default_factory=datetime.now)


class EmailLogSchema(BaseModel):
    """Schema for email sending logs (future enhancement)."""
    log_id: str
    session_id: Optional[str] = None
    user_id: str
    recipients: List[EmailStr]
    subject: str
    status: str = Field(regex="^(queued|sent|failed|bounced)$")
    sent_at: Optional[datetime] = None
    error_message: Optional[str] = None
    created_at: datetime = Field(default_factory=datetime.now)


class EmailStatsSchema(BaseModel):
    """Schema for email statistics (future enhancement)."""
    total_sent: int = 0
    total_failed: int = 0
    total_queued: int = 0
    success_rate: float = 0.0
    last_sent: Optional[datetime] = None
    period_start: datetime
    period_end: datetime
