import os
import smtplib
from typing import Dict, List, Optional
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON>ultipart
from email.mime.base import MIMEBase
from email import encoders
from datetime import datetime
import logging
from pydantic import BaseModel, EmailStr, validator

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class EmailConfig(BaseModel):
    """Email configuration model."""
    smtp_server: str
    smtp_port: int
    sender_email: EmailStr
    sender_password: str
    sender_name: Optional[str] = None
    use_tls: bool = True
    use_ssl: bool = False
    
    @validator('smtp_port')
    def validate_port(cls, v):
        if not 1 <= v <= 65535:
            raise ValueError('Port must be between 1 and 65535')
        return v


class EmailData(BaseModel):
    """Email data model for validation."""
    to: List[EmailStr]
    cc: Optional[List[EmailStr]] = []
    bcc: Optional[List[EmailStr]] = []
    subject: str
    body: str
    body_type: str = "plain"  # "plain" or "html"
    priority: str = "normal"  # "low", "normal", "high"
    attachments: Optional[List[str]] = []
    template: Optional[str] = None
    scheduled_send: Optional[str] = None


class EmailService:
    """Service for sending emails via SMTP."""
    
    def __init__(self):
        self.config = self._load_config()
    
    def _load_config(self) -> EmailConfig:
        """Load email configuration from environment variables."""
        try:
            return EmailConfig(
                smtp_server=os.getenv("SMTP_SERVER", "smtp.gmail.com"),
                smtp_port=int(os.getenv("SMTP_PORT", "587")),
                sender_email=os.getenv("SENDER_EMAIL", ""),
                sender_password=os.getenv("SENDER_PASSWORD", ""),
                sender_name=os.getenv("SENDER_NAME", "Magento Bot"),
                use_tls=os.getenv("SMTP_USE_TLS", "true").lower() == "true",
                use_ssl=os.getenv("SMTP_USE_SSL", "false").lower() == "true",
            )
        except Exception as e:
            logger.error(f"Failed to load email configuration: {str(e)}")
            raise ValueError(f"Invalid email configuration: {str(e)}")
    
    def validate_config(self) -> Dict[str, any]:
        """Validate email configuration."""
        issues = []
        
        if not self.config.sender_email:
            issues.append("SENDER_EMAIL environment variable is required")
        
        if not self.config.sender_password:
            issues.append("SENDER_PASSWORD environment variable is required")
        
        if not self.config.smtp_server:
            issues.append("SMTP_SERVER environment variable is required")
        
        return {
            "is_valid": len(issues) == 0,
            "issues": issues,
            "config": {
                "smtp_server": self.config.smtp_server,
                "smtp_port": self.config.smtp_port,
                "sender_email": self.config.sender_email,
                "sender_name": self.config.sender_name,
                "use_tls": self.config.use_tls,
                "use_ssl": self.config.use_ssl,
            }
        }
    
    def test_connection(self) -> Dict[str, any]:
        """Test SMTP connection."""
        try:
            if self.config.use_ssl:
                server = smtplib.SMTP_SSL(self.config.smtp_server, self.config.smtp_port)
            else:
                server = smtplib.SMTP(self.config.smtp_server, self.config.smtp_port)
                if self.config.use_tls:
                    server.starttls()
            
            server.login(self.config.sender_email, self.config.sender_password)
            server.quit()
            
            return {
                "success": True,
                "message": "SMTP connection successful",
                "timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            logger.error(f"SMTP connection test failed: {str(e)}")
            return {
                "success": False,
                "message": f"SMTP connection failed: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }
    
    def send_email(self, email_data: EmailData) -> Dict[str, any]:
        """Send email using SMTP."""
        try:
            # Validate configuration first
            config_validation = self.validate_config()
            if not config_validation["is_valid"]:
                return {
                    "success": False,
                    "message": "Email configuration is invalid",
                    "errors": config_validation["issues"],
                    "timestamp": datetime.now().isoformat()
                }
            
            # Create message
            msg = MIMEMultipart()
            msg['From'] = f"{self.config.sender_name} <{self.config.sender_email}>" if self.config.sender_name else self.config.sender_email
            msg['To'] = ", ".join(email_data.to)
            
            if email_data.cc:
                msg['Cc'] = ", ".join(email_data.cc)
            
            msg['Subject'] = email_data.subject
            
            # Set priority
            if email_data.priority == "high":
                msg['X-Priority'] = '1'
                msg['X-MSMail-Priority'] = 'High'
            elif email_data.priority == "low":
                msg['X-Priority'] = '5'
                msg['X-MSMail-Priority'] = 'Low'
            
            # Add body
            if email_data.body_type == "html":
                msg.attach(MIMEText(email_data.body, 'html'))
            else:
                msg.attach(MIMEText(email_data.body, 'plain'))
            
            # Add attachments (if any)
            for attachment_path in email_data.attachments or []:
                if os.path.exists(attachment_path):
                    with open(attachment_path, "rb") as attachment:
                        part = MIMEBase('application', 'octet-stream')
                        part.set_payload(attachment.read())
                    
                    encoders.encode_base64(part)
                    part.add_header(
                        'Content-Disposition',
                        f'attachment; filename= {os.path.basename(attachment_path)}'
                    )
                    msg.attach(part)
                else:
                    logger.warning(f"Attachment not found: {attachment_path}")
            
            # Connect to server and send email
            if self.config.use_ssl:
                server = smtplib.SMTP_SSL(self.config.smtp_server, self.config.smtp_port)
            else:
                server = smtplib.SMTP(self.config.smtp_server, self.config.smtp_port)
                if self.config.use_tls:
                    server.starttls()
            
            server.login(self.config.sender_email, self.config.sender_password)
            
            # Prepare recipient list
            recipients = email_data.to + (email_data.cc or []) + (email_data.bcc or [])
            
            # Send email
            text = msg.as_string()
            server.sendmail(self.config.sender_email, recipients, text)
            server.quit()
            
            logger.info(f"Email sent successfully to {', '.join(email_data.to)}")
            
            return {
                "success": True,
                "message": "Email sent successfully",
                "recipients": {
                    "to": email_data.to,
                    "cc": email_data.cc or [],
                    "bcc": email_data.bcc or []
                },
                "subject": email_data.subject,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            logger.error(f"Failed to send email: {str(e)}")
            return {
                "success": False,
                "message": f"Failed to send email: {str(e)}",
                "timestamp": datetime.now().isoformat()
            }


# Global email service instance
email_service = EmailService()
