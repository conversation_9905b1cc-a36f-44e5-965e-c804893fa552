import React, { useState } from 'react';
import {
  Box,
  Chip,
  Collapse,
  Typography,
  Paper,
  IconButton,
  List,
  ListItem,
  ListItemText,
  Divider,
} from '@mui/material';
import {
  Help as HelpIcon,
  Close as CloseIcon,
  Email as EmailIcon,
} from '@mui/icons-material';

interface EmailCommandHelperProps {
  message: string;
  onInsertExample: (example: string) => void;
}

const EmailCommandHelper: React.FC<EmailCommandHelperProps> = ({ 
  message, 
  onInsertExample 
}) => {
  const [showHelp, setShowHelp] = useState(false);

  // Check if user is typing an email command
  const isTypingEmailCommand = message.toLowerCase().startsWith('/email');

  const emailExamples = [
    {
      title: 'Basic Email',
      command: '/<NAME_EMAIL> subject "Meeting Tomorrow" body "Hi John, let\'s meet tomorrow at 2 PM"',
      description: 'Send a simple email with recipient, subject, and body'
    },
    {
      title: 'Email with <PERSON>',
      command: '/<NAME_EMAIL> cc <EMAIL> subject "Project Update" body "Here is the latest project update..."',
      description: 'Send email with CC recipients'
    },
    {
      title: 'Natural Language',
      command: '/email <NAME_EMAIL> about invoice #12345',
      description: 'Use natural language to compose email'
    },
    {
      title: 'High Priority',
      command: '/<NAME_EMAIL> subject "URGENT: Server Down" body "The main server is down, please check immediately"',
      description: 'Send high priority email'
    }
  ];

  if (!isTypingEmailCommand && !showHelp) {
    return null;
  }

  return (
    <Box sx={{ mb: 1 }}>
      {/* Email Command Indicator */}
      {isTypingEmailCommand && (
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
          <Chip
            icon={<EmailIcon />}
            label="Email Command Detected"
            color="primary"
            size="small"
            sx={{ mr: 1 }}
          />
          <IconButton
            size="small"
            onClick={() => setShowHelp(!showHelp)}
            sx={{ ml: 'auto' }}
          >
            <HelpIcon fontSize="small" />
          </IconButton>
        </Box>
      )}

      {/* Help Panel */}
      <Collapse in={showHelp || isTypingEmailCommand}>
        <Paper
          elevation={2}
          sx={{
            p: 2,
            bgcolor: 'background.paper',
            border: '1px solid',
            borderColor: 'divider',
            borderRadius: 2,
          }}
        >
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <EmailIcon sx={{ mr: 1, color: 'primary.main' }} />
            <Typography variant="h6" component="div" sx={{ flexGrow: 1 }}>
              Email Command Help
            </Typography>
            <IconButton
              size="small"
              onClick={() => setShowHelp(false)}
            >
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>

          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Use the /email command to compose and send emails. Click on any example below to use it:
          </Typography>

          <List dense>
            {emailExamples.map((example, index) => (
              <React.Fragment key={index}>
                <ListItem
                  button
                  onClick={() => onInsertExample(example.command)}
                  sx={{
                    borderRadius: 1,
                    mb: 1,
                    '&:hover': {
                      bgcolor: 'action.hover',
                    },
                  }}
                >
                  <ListItemText
                    primary={example.title}
                    secondary={
                      <Box>
                        <Typography variant="body2" color="text.secondary">
                          {example.description}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            fontFamily: 'monospace',
                            bgcolor: 'grey.100',
                            p: 0.5,
                            borderRadius: 0.5,
                            display: 'block',
                            mt: 0.5,
                          }}
                        >
                          {example.command}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
                {index < emailExamples.length - 1 && <Divider />}
              </React.Fragment>
            ))}
          </List>

          <Box sx={{ mt: 2, p: 1, bgcolor: 'info.light', borderRadius: 1 }}>
            <Typography variant="caption" color="info.contrastText">
              <strong>Tip:</strong> You can use natural language patterns like "send email to..." or "compose message to..."
            </Typography>
          </Box>
        </Paper>
      </Collapse>
    </Box>
  );
};

export default EmailCommandHelper;
