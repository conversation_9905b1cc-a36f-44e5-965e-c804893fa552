import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON>,
  <PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Alert,
  CircularProgress,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Email as EmailIcon,
  Send as SendIcon,
  Warning as WarningIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  ExpandMore as ExpandMoreIcon,
  Person as PersonIcon,
  Subject as SubjectIcon,
  Message as MessageIcon,
} from '@mui/icons-material';
import { sendEmailRequest } from '@/services/api';
import { enqueueSnackbar } from 'notistack';

interface EmailData {
  to: string[];
  cc?: string[];
  bcc?: string[];
  subject: string;
  body: string;
  body_type: string;
  priority: string;
  attachments?: string[];
}

interface EmailValidation {
  is_valid: boolean;
  missing_fields: string[];
  warnings: string[];
}

interface EmailMetadata {
  command_detected: boolean;
  processing_timestamp: string;
  agent_version: string;
}

interface EmailResponseData {
  email_data: EmailData;
  validation: EmailValidation;
  metadata: EmailMetadata;
}

interface EmailResponseProps {
  emailData: EmailResponseData;
  onEmailSent?: () => void;
}

const EmailResponse: React.FC<EmailResponseProps> = ({ emailData, onEmailSent }) => {
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [sending, setSending] = useState(false);
  const [sendResult, setSendResult] = useState<any>(null);

  const handleSendEmail = async () => {
    setSending(true);
    try {
      const response = await sendEmailRequest({
        email_data: emailData.email_data
      });

      if (response.status === 200) {
        setSendResult({ success: true, data: response.data });
        enqueueSnackbar('Email sent successfully!', { variant: 'success' });
        onEmailSent?.();
      } else {
        setSendResult({ success: false, error: 'Failed to send email' });
        enqueueSnackbar('Failed to send email', { variant: 'error' });
      }
    } catch (error: any) {
      console.error('Email send error:', error);
      setSendResult({ 
        success: false, 
        error: error.response?.data?.detail || 'Failed to send email' 
      });
      enqueueSnackbar('Failed to send email', { variant: 'error' });
    } finally {
      setSending(false);
      setConfirmDialogOpen(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'error';
      case 'low': return 'info';
      default: return 'default';
    }
  };

  const getValidationIcon = (isValid: boolean) => {
    return isValid ? (
      <CheckCircleIcon color="success" />
    ) : (
      <ErrorIcon color="error" />
    );
  };

  return (
    <Box sx={{ mb: 2 }}>
      <Card elevation={2} sx={{ borderLeft: '4px solid #1976d2' }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <EmailIcon sx={{ mr: 1, color: '#1976d2' }} />
            <Typography variant="h6" component="div">
              Email Composition
            </Typography>
            <Box sx={{ ml: 'auto' }}>
              {getValidationIcon(emailData.validation.is_valid)}
            </Box>
          </Box>

          {/* Validation Status */}
          {!emailData.validation.is_valid && (
            <Alert severity="warning" sx={{ mb: 2 }}>
              <Typography variant="body2">
                Email validation failed. Missing fields: {emailData.validation.missing_fields.join(', ')}
              </Typography>
            </Alert>
          )}

          {emailData.validation.warnings.length > 0 && (
            <Alert severity="info" sx={{ mb: 2 }}>
              <Typography variant="body2">
                Warnings: {emailData.validation.warnings.join(', ')}
              </Typography>
            </Alert>
          )}

          {/* Email Details */}
          <List dense>
            <ListItem>
              <ListItemIcon>
                <PersonIcon />
              </ListItemIcon>
              <ListItemText
                primary="Recipients"
                secondary={emailData.email_data.to.join(', ')}
              />
            </ListItem>

            {emailData.email_data.cc && emailData.email_data.cc.length > 0 && (
              <ListItem>
                <ListItemIcon>
                  <PersonIcon />
                </ListItemIcon>
                <ListItemText
                  primary="CC"
                  secondary={emailData.email_data.cc.join(', ')}
                />
              </ListItem>
            )}

            <ListItem>
              <ListItemIcon>
                <SubjectIcon />
              </ListItemIcon>
              <ListItemText
                primary="Subject"
                secondary={emailData.email_data.subject}
              />
            </ListItem>

            <ListItem>
              <ListItemIcon>
                <MessageIcon />
              </ListItemIcon>
              <ListItemText
                primary="Body Preview"
                secondary={
                  emailData.email_data.body.length > 100
                    ? `${emailData.email_data.body.substring(0, 100)}...`
                    : emailData.email_data.body
                }
              />
            </ListItem>
          </List>

          {/* Email Properties */}
          <Box sx={{ display: 'flex', gap: 1, mt: 2, mb: 2 }}>
            <Chip
              label={`Priority: ${emailData.email_data.priority}`}
              color={getPriorityColor(emailData.email_data.priority) as any}
              size="small"
            />
            <Chip
              label={`Type: ${emailData.email_data.body_type}`}
              variant="outlined"
              size="small"
            />
            {emailData.email_data.attachments && emailData.email_data.attachments.length > 0 && (
              <Chip
                label={`${emailData.email_data.attachments.length} attachment(s)`}
                variant="outlined"
                size="small"
              />
            )}
          </Box>

          {/* Expandable Full Body */}
          <Accordion>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="body2">View Full Email Body</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Box
                sx={{
                  p: 2,
                  bgcolor: 'grey.50',
                  borderRadius: 1,
                  maxHeight: 200,
                  overflow: 'auto',
                }}
              >
                <Typography
                  variant="body2"
                  component="pre"
                  sx={{ whiteSpace: 'pre-wrap', fontFamily: 'monospace' }}
                >
                  {emailData.email_data.body}
                </Typography>
              </Box>
            </AccordionDetails>
          </Accordion>

          <Divider sx={{ my: 2 }} />

          {/* Action Buttons */}
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
            <Button
              variant="contained"
              startIcon={<SendIcon />}
              onClick={() => setConfirmDialogOpen(true)}
              disabled={!emailData.validation.is_valid || sending}
            >
              Send Email
            </Button>
          </Box>

          {/* Send Result */}
          {sendResult && (
            <Box sx={{ mt: 2 }}>
              <Alert severity={sendResult.success ? 'success' : 'error'}>
                {sendResult.success
                  ? 'Email sent successfully!'
                  : `Failed to send email: ${sendResult.error}`}
              </Alert>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialogOpen} onClose={() => setConfirmDialogOpen(false)}>
        <DialogTitle>Confirm Email Send</DialogTitle>
        <DialogContent>
          <Typography variant="body1" gutterBottom>
            Are you sure you want to send this email?
          </Typography>
          <Box sx={{ mt: 2 }}>
            <Typography variant="body2" color="text.secondary">
              <strong>To:</strong> {emailData.email_data.to.join(', ')}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              <strong>Subject:</strong> {emailData.email_data.subject}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleSendEmail}
            variant="contained"
            disabled={sending}
            startIcon={sending ? <CircularProgress size={16} /> : <SendIcon />}
          >
            {sending ? 'Sending...' : 'Send Email'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default EmailResponse;
